/* JSONEditor 自定义样式 - 与现有页面保持一致 */

/* 主容器样式 */
.jsoneditor {
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* 工具栏样式 */
.jsoneditor-menu {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-bottom: 1px solid #dee2e6 !important;
    border-radius: 4px 4px 0 0 !important;
}

/* 按钮样式 */
.jsoneditor-menu > button,
.jsoneditor-menu > .jsoneditor-modes > button {
    background: #fff !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 3px !important;
    color: #495057 !important;
    font-size: 12px !important;
    padding: 4px 8px !important;
    margin: 2px !important;
    transition: all 0.2s ease !important;
}

.jsoneditor-menu > button:hover,
.jsoneditor-menu > .jsoneditor-modes > button:hover {
    background: #007bff !important;
    color: #fff !important;
    border-color: #007bff !important;
}

.jsoneditor-menu > button.jsoneditor-selected,
.jsoneditor-menu > .jsoneditor-modes > button.jsoneditor-selected {
    background: #007bff !important;
    color: #fff !important;
    border-color: #007bff !important;
}

/* 展开/折叠所有按钮样式 */
.jsoneditor-expand-all {
    background: #fff !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 3px !important;
    color: #495057 !important;
    font-size: 12px !important;
    padding: 4px 8px !important;
    margin: 2px !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    min-width: 28px !important;
    height: 24px !important;
}

.jsoneditor-expand-all {
    background-image: none !important; /* 移除默认背景图像 */
}

.jsoneditor-expand-all:before {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    content: "\f065" !important; /* fa-expand-arrows-alt */
    font-size: 11px !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

.jsoneditor-expand-all:hover {
    background: #007bff !important;
    color: #fff !important;
    border-color: #007bff !important;
}

.jsoneditor-expand-all.jsoneditor-collapsed:before {
    content: "\f066" !important; /* fa-compress-arrows-alt */
}

/* 其他工具栏按钮样式 */
.jsoneditor-menu .jsoneditor-sort,
.jsoneditor-menu .jsoneditor-transform,
.jsoneditor-menu .jsoneditor-compact,
.jsoneditor-menu .jsoneditor-format,
.jsoneditor-menu .jsoneditor-repair {
    background: #fff !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 3px !important;
    color: #495057 !important;
    font-size: 12px !important;
    padding: 4px 8px !important;
    margin: 2px !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    min-width: 28px !important;
    height: 24px !important;
}

.jsoneditor-menu .jsoneditor-sort:hover,
.jsoneditor-menu .jsoneditor-transform:hover,
.jsoneditor-menu .jsoneditor-compact:hover,
.jsoneditor-menu .jsoneditor-format:hover,
.jsoneditor-menu .jsoneditor-repair:hover {
    background: #007bff !important;
    color: #fff !important;
    border-color: #007bff !important;
}

/* 排序按钮图标 */
.jsoneditor-menu .jsoneditor-sort:before {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    content: "\f0dc" !important; /* fa-sort */
    font-size: 10px !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

/* 转换按钮图标 */
.jsoneditor-menu .jsoneditor-transform:before {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    content: "\f0ec" !important; /* fa-exchange-alt */
    font-size: 10px !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

/* 压缩按钮图标 */
.jsoneditor-menu .jsoneditor-compact:before {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    content: "\f066" !important; /* fa-compress-arrows-alt */
    font-size: 10px !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

/* 格式化按钮图标 */
.jsoneditor-menu .jsoneditor-format:before {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    content: "\f1dd" !important; /* fa-align-left */
    font-size: 10px !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

/* 修复按钮图标 */
.jsoneditor-menu .jsoneditor-repair:before {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    content: "\f0ad" !important; /* fa-wrench */
    font-size: 10px !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

/* 搜索框样式 */
.jsoneditor-search input {
    border: 1px solid #ced4da !important;
    border-radius: 3px !important;
    padding: 4px 8px !important;
    font-size: 12px !important;
}

.jsoneditor-search input:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    outline: none !important;
}

/* 搜索框按钮样式 */
.jsoneditor-search .jsoneditor-frame {
    display: flex !important;
    align-items: center !important;
    gap: 2px !important;
}

.jsoneditor-search .jsoneditor-refresh,
.jsoneditor-search .jsoneditor-next,
.jsoneditor-search .jsoneditor-previous {
    background: #fff !important;
    background-image: none !important; /* 移除默认背景图像 */
    border: 1px solid #dee2e6 !important;
    border-radius: 3px !important;
    color: #495057 !important;
    width: 24px !important;
    height: 24px !important;
    padding: 0 !important;
    margin: 0 1px !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    cursor: pointer !important;
}

.jsoneditor-search .jsoneditor-refresh:hover,
.jsoneditor-search .jsoneditor-next:hover,
.jsoneditor-search .jsoneditor-previous:hover {
    background: #007bff !important;
    color: #fff !important;
    border-color: #007bff !important;
}

/* 刷新按钮图标 */
.jsoneditor-search .jsoneditor-refresh:before {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    content: "\f021" !important; /* fa-sync */
    font-size: 10px !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

/* 下一个结果按钮图标 */
.jsoneditor-search .jsoneditor-next:before {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    content: "\f107" !important; /* fa-angle-down */
    font-size: 10px !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

/* 上一个结果按钮图标 */
.jsoneditor-search .jsoneditor-previous:before {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    content: "\f106" !important; /* fa-angle-up */
    font-size: 10px !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

/* 树形视图样式 */
.jsoneditor-tree {
    background: #fff !important;
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
}

/* 树节点样式 */
.jsoneditor-tree .jsoneditor-node {
    margin: 2px 0 !important;
    padding: 2px 0 !important;
}

/* 树节点展开/折叠按钮 */
.jsoneditor-tree .jsoneditor-button {
    width: 18px !important;
    height: 18px !important;
    background-image: none !important; /* 移除默认背景图像 */
    background-size: 12px 12px !important;
    background-position: center !important;
    border-radius: 3px !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    border: 1px solid transparent !important;
}

.jsoneditor-tree .jsoneditor-button:hover {
    background-color: #e9ecef !important;
    border-color: #dee2e6 !important;
}

/* 展开状态的按钮图标 */
.jsoneditor-tree .jsoneditor-expanded > .jsoneditor-button:before {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    content: "\f107" !important; /* fa-angle-down */
    font-size: 10px !important;
    color: #495057 !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

/* 折叠状态的按钮图标 */
.jsoneditor-tree .jsoneditor-collapsed > .jsoneditor-button:before {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    content: "\f105" !important; /* fa-angle-right */
    font-size: 10px !important;
    color: #495057 !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

/* 树节点字段名样式 */
.jsoneditor-tree .jsoneditor-field {
    color: #0066cc !important;
    font-weight: 600 !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    background: rgba(0, 102, 204, 0.1) !important;
    margin-right: 6px !important;
}

/* 树节点值样式 */
.jsoneditor-tree .jsoneditor-value {
    color: #333 !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    background: rgba(0, 0, 0, 0.05) !important;
}

/* 不同类型的值使用不同颜色 */
.jsoneditor-tree .jsoneditor-value.jsoneditor-string {
    color: #d73a49 !important;
    background: rgba(215, 58, 73, 0.1) !important;
}

.jsoneditor-tree .jsoneditor-value.jsoneditor-number {
    color: #005cc5 !important;
    background: rgba(0, 92, 197, 0.1) !important;
}

.jsoneditor-tree .jsoneditor-value.jsoneditor-boolean {
    color: #e36209 !important;
    background: rgba(227, 98, 9, 0.1) !important;
    font-weight: 600 !important;
}

.jsoneditor-tree .jsoneditor-value.jsoneditor-null {
    color: #6f42c1 !important;
    background: rgba(111, 66, 193, 0.1) !important;
    font-style: italic !important;
}

/* 数组和对象的括号样式 */
.jsoneditor-tree .jsoneditor-bracket {
    color: #666 !important;
    font-weight: bold !important;
}

/* 树节点悬停效果 */
.jsoneditor-tree .jsoneditor-node:hover {
    background-color: rgba(0, 123, 255, 0.05) !important;
    border-radius: 4px !important;
}

/* 选中的树节点 */
.jsoneditor-tree .jsoneditor-node.jsoneditor-selected {
    background-color: rgba(0, 123, 255, 0.15) !important;
    border-radius: 4px !important;
    border-left: 3px solid #007bff !important;
    padding-left: 8px !important;
}

/* 树节点的缩进线 */
.jsoneditor-tree .jsoneditor-node .jsoneditor-button + .jsoneditor-node {
    border-left: 1px dashed #ddd !important;
    margin-left: 9px !important;
    padding-left: 10px !important;
}

/* 根节点样式 */
.jsoneditor-tree > .jsoneditor-node {
    border: none !important;
    margin-left: 0 !important;
    padding-left: 0 !important;
}

/* 数组索引样式 */
.jsoneditor-tree .jsoneditor-field.jsoneditor-index {
    color: #6f42c1 !important;
    background: rgba(111, 66, 193, 0.1) !important;
    font-weight: normal !important;
    font-style: italic !important;
}

/* 树的整体容器美化 */
.jsoneditor-tree-inner {
    padding: 8px !important;
}

/* 改善树节点的可读性 */
.jsoneditor-tree .jsoneditor-node {
    transition: all 0.2s ease !important;
}

/* 对象和数组的展开状态指示 */
.jsoneditor-tree .jsoneditor-expanded > .jsoneditor-button {
    background-color: rgba(0, 123, 255, 0.1) !important;
}

.jsoneditor-tree .jsoneditor-collapsed > .jsoneditor-button {
    background-color: rgba(108, 117, 125, 0.1) !important;
}

/* 树节点的类型标识 */
.jsoneditor-tree .jsoneditor-node.jsoneditor-object > .jsoneditor-button::before {
    content: "{}";
    font-size: 10px;
    color: #007bff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
}

.jsoneditor-tree .jsoneditor-node.jsoneditor-array > .jsoneditor-button::before {
    content: "[]";
    font-size: 10px;
    color: #28a745;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
}

/* 改善搜索高亮效果 */
.jsoneditor-tree .jsoneditor-highlight {
    background-color: #fff3cd !important;
    border: 1px solid #ffeaa7 !important;
    border-radius: 3px !important;
    padding: 1px 3px !important;
}

.jsoneditor-tree .jsoneditor-highlight-active {
    background-color: #ffc107 !important;
    color: #212529 !important;
    font-weight: bold !important;
}

/* 错误状态的节点 */
.jsoneditor-tree .jsoneditor-node.jsoneditor-validation-error {
    border-left: 3px solid #dc3545 !important;
    background-color: rgba(220, 53, 69, 0.1) !important;
}

/* 树节点的工具提示 */
.jsoneditor-tree .jsoneditor-node[title] {
    cursor: help !important;
}

/* 改善树的滚动条样式 */
.jsoneditor-tree::-webkit-scrollbar {
    width: 8px !important;
}

.jsoneditor-tree::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 4px !important;
}

.jsoneditor-tree::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 4px !important;
}

.jsoneditor-tree::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}

/* 代码编辑器样式 */
.jsoneditor-code .ace_editor {
    font-family: 'Courier New', monospace !important;
    font-size: 13px !important;
}

/* 状态栏样式 */
.jsoneditor-statusbar {
    background: #f8f9fa !important;
    border-top: 1px solid #dee2e6 !important;
    color: #6c757d !important;
    font-size: 11px !important;
    padding: 4px 8px !important;
}

/* 错误提示样式 */
.jsoneditor-validation-errors {
    background: #f8d7da !important;
    border: 1px solid #f5c6cb !important;
    border-radius: 3px !important;
    color: #721c24 !important;
    padding: 8px !important;
    margin: 4px 0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .jsoneditor {
        height: 400px !important;
    }
    
    .jsoneditor-menu {
        flex-wrap: wrap !important;
    }
    
    .jsoneditor-menu > button,
    .jsoneditor-menu > .jsoneditor-modes > button {
        font-size: 11px !important;
        padding: 3px 6px !important;
    }
}

/* 深色主题兼容 */
@media (prefers-color-scheme: dark) {
    .jsoneditor {
        background: #2d3748 !important;
        border-color: #4a5568 !important;
    }

    .jsoneditor-menu {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%) !important;
        border-bottom-color: #4a5568 !important;
    }

    .jsoneditor-tree {
        background: #2d3748 !important;
        color: #e2e8f0 !important;
    }

    .jsoneditor-statusbar {
        background: #1a202c !important;
        border-top-color: #4a5568 !important;
        color: #a0aec0 !important;
    }

    /* 深色主题下的树节点样式 */
    .jsoneditor-tree .jsoneditor-field {
        color: #63b3ed !important;
        background: rgba(99, 179, 237, 0.2) !important;
    }

    .jsoneditor-tree .jsoneditor-value {
        color: #e2e8f0 !important;
        background: rgba(255, 255, 255, 0.1) !important;
    }

    .jsoneditor-tree .jsoneditor-value.jsoneditor-string {
        color: #f56565 !important;
        background: rgba(245, 101, 101, 0.2) !important;
    }

    .jsoneditor-tree .jsoneditor-value.jsoneditor-number {
        color: #4299e1 !important;
        background: rgba(66, 153, 225, 0.2) !important;
    }

    .jsoneditor-tree .jsoneditor-value.jsoneditor-boolean {
        color: #ed8936 !important;
        background: rgba(237, 137, 54, 0.2) !important;
    }

    .jsoneditor-tree .jsoneditor-value.jsoneditor-null {
        color: #b794f6 !important;
        background: rgba(183, 148, 246, 0.2) !important;
    }

    .jsoneditor-tree .jsoneditor-node:hover {
        background-color: rgba(99, 179, 237, 0.1) !important;
    }

    .jsoneditor-tree .jsoneditor-node.jsoneditor-selected {
        background-color: rgba(99, 179, 237, 0.2) !important;
        border-left-color: #63b3ed !important;
    }

    .jsoneditor-tree .jsoneditor-button:hover {
        background-color: #4a5568 !important;
    }

    .jsoneditor-tree .jsoneditor-field.jsoneditor-index {
        color: #b794f6 !important;
        background: rgba(183, 148, 246, 0.2) !important;
    }

    /* 深色主题下的按钮样式 */
    .jsoneditor-menu > button,
    .jsoneditor-menu > .jsoneditor-modes > button,
    .jsoneditor-expand-all,
    .jsoneditor-search .jsoneditor-refresh,
    .jsoneditor-search .jsoneditor-next,
    .jsoneditor-search .jsoneditor-previous {
        background: #4a5568 !important;
        border-color: #718096 !important;
        color: #e2e8f0 !important;
    }

    .jsoneditor-menu > button:hover,
    .jsoneditor-menu > .jsoneditor-modes > button:hover,
    .jsoneditor-expand-all:hover,
    .jsoneditor-search .jsoneditor-refresh:hover,
    .jsoneditor-search .jsoneditor-next:hover,
    .jsoneditor-search .jsoneditor-previous:hover {
        background: #63b3ed !important;
        border-color: #63b3ed !important;
        color: #1a202c !important;
    }

    .jsoneditor-menu > button.jsoneditor-selected,
    .jsoneditor-menu > .jsoneditor-modes > button.jsoneditor-selected {
        background: #63b3ed !important;
        border-color: #63b3ed !important;
        color: #1a202c !important;
    }

    /* 深色主题下的树节点按钮 */
    .jsoneditor-tree .jsoneditor-expanded > .jsoneditor-button:before,
    .jsoneditor-tree .jsoneditor-collapsed > .jsoneditor-button:before {
        color: #e2e8f0 !important;
    }

    .jsoneditor-tree .jsoneditor-button:hover {
        background-color: #4a5568 !important;
        border-color: #718096 !important;
    }

    /* 深色主题下的搜索框 */
    .jsoneditor-search input {
        background: #4a5568 !important;
        border-color: #718096 !important;
        color: #e2e8f0 !important;
    }

    .jsoneditor-search input:focus {
        border-color: #63b3ed !important;
        box-shadow: 0 0 0 0.2rem rgba(99, 179, 237, 0.25) !important;
    }
}

/* 确保与Bootstrap样式兼容 */
.jsoneditor * {
    box-sizing: border-box !important;
}

/* 修复可能的z-index问题 */
.jsoneditor-modal,
.jsoneditor-contextmenu {
    z-index: 1060 !important;
}

/* 配置编辑器容器样式 */
#configEditor {
    height: 600px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* 验证结果容器 */
#validationResult {
    display: none;
}

/* 专业配置模式面板 */
#advancedConfigPanel {
    display: none;
}

/* 通知容器 */
#notification {
    z-index: 1055;
}

/* 其他JSONEditor按钮的通用样式 */
.jsoneditor button,
.jsoneditor input[type="button"] {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    transition: all 0.2s ease !important;
}

/* 确保所有按钮都有正确的光标 */
.jsoneditor button:not(:disabled),
.jsoneditor input[type="button"]:not(:disabled) {
    cursor: pointer !important;
}

/* 禁用状态的按钮 */
.jsoneditor button:disabled,
.jsoneditor input[type="button"]:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
}

/* 上下文菜单按钮样式 */
.jsoneditor-contextmenu button {
    background: #fff !important;
    border: none !important;
    color: #495057 !important;
    padding: 8px 12px !important;
    text-align: left !important;
    width: 100% !important;
    transition: all 0.2s ease !important;
}

.jsoneditor-contextmenu button:hover {
    background: #007bff !important;
    color: #fff !important;
}

/* 模态框按钮样式 */
.jsoneditor-modal button {
    background: #fff !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 3px !important;
    color: #495057 !important;
    padding: 6px 12px !important;
    margin: 0 4px !important;
    transition: all 0.2s ease !important;
}

.jsoneditor-modal button:hover {
    background: #007bff !important;
    color: #fff !important;
    border-color: #007bff !important;
}

.jsoneditor-modal button.jsoneditor-primary {
    background: #007bff !important;
    color: #fff !important;
    border-color: #007bff !important;
}

.jsoneditor-modal button.jsoneditor-primary:hover {
    background: #0056b3 !important;
    border-color: #0056b3 !important;
}
